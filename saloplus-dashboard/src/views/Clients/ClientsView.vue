<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <button
          @click="$router.go(-1)"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <ArrowLeftIcon class="h-4 w-4 mr-2" />
          Back
        </button>
        <div>
          <h1 class="text-2xl font-bold text-gray-900">{{ client?.client_name || 'Client Details' }}</h1>
          <p class="mt-1 text-sm text-gray-500">
            Account: {{ client?.client_account || 'Loading...' }}
          </p>
        </div>
      </div>
      <div class="flex items-center space-x-3">
        <ActionButton
          variant="edit"
          size="md"
          shape="rounded"
          tooltip="Edit Client"
          @click="editClient"
        >
          <template #icon>
            <PencilIcon class="h-4 w-4" />
          </template>
          Edit
        </ActionButton>
        <ActionButton
          :variant="client?.client_status === '1' ? 'danger' : 'success'"
          size="md"
          shape="rounded"
          :tooltip="client?.client_status === '1' ? 'Deactivate Client' : 'Activate Client'"
          @click="toggleStatus"
        >
          <template #icon>
            <XMarkIcon v-if="client?.client_status === '1'" class="h-4 w-4" />
            <CheckIcon v-else class="h-4 w-4" />
          </template>
          {{ client?.client_status === '1' ? 'Deactivate' : 'Activate' }}
        </ActionButton>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-md p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <XMarkIcon class="h-5 w-5 text-red-400" />
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Error loading client details</h3>
          <div class="mt-2 text-sm text-red-700">
            <p>{{ error }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Client Details Card -->
    <div v-else-if="client" class="bg-white rounded-xl shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">Client Information</h3>
          <span
            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
            :class="{
              'bg-green-100 text-green-800': client.client_status === '1',
              'bg-red-100 text-red-800': client.client_status === '0'
            }"
          >
            {{ client.client_status === '1' ? 'Active' : 'Inactive' }}
          </span>
        </div>
      </div>
      
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <!-- Basic Information -->
          <div class="space-y-4">
            <h4 class="text-sm font-medium text-gray-900 uppercase tracking-wide">Basic Information</h4>
            <div class="space-y-3">
              <div>
                <dt class="text-sm font-medium text-gray-500">Client Name</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ client.client_name }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Account Number</dt>
                <dd class="mt-1 text-sm text-gray-900 font-mono">{{ client.client_account }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Currency</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ client.currency_code }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Service Fee</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ (parseFloat(client.service_fee) * 100).toFixed(3) }}%</dd>
              </div>
            </div>
          </div>

          <!-- Contact Information -->
          <div class="space-y-4">
            <h4 class="text-sm font-medium text-gray-900 uppercase tracking-wide">Contact Information</h4>
            <div class="space-y-3">
              <div>
                <dt class="text-sm font-medium text-gray-500">Email Address</dt>
                <dd class="mt-1 text-sm text-gray-900">
                  <a :href="`mailto:${client.client_email}`" class="text-blue-600 hover:text-blue-500">
                    {{ client.client_email }}
                  </a>
                </dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Phone Number</dt>
                <dd class="mt-1 text-sm text-gray-900">
                  <a :href="`tel:${client.client_phone}`" class="text-blue-600 hover:text-blue-500">
                    {{ client.client_phone }}
                  </a>
                </dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Address</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ client.client_address || 'Not provided' }}</dd>
              </div>
            </div>
          </div>

          <!-- Business Information -->
          <div class="space-y-4">
            <h4 class="text-sm font-medium text-gray-900 uppercase tracking-wide">Business Information</h4>
            <div class="space-y-3">
              <div>
                <dt class="text-sm font-medium text-gray-500">Can Issue Loans</dt>
                <dd class="mt-1">
                  <span
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    :class="{
                      'bg-green-100 text-green-800': client.can_issue_loans === '1',
                      'bg-red-100 text-red-800': client.can_issue_loans === '0'
                    }"
                  >
                    {{ client.can_issue_loans === '1' ? 'Yes' : 'No' }}
                  </span>
                </dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Total Loan Assets</dt>
                <dd class="mt-1 text-sm text-gray-900 font-medium">{{ formatCurrency(client.total_loan_assets) }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Operating Hours</dt>
                <dd class="mt-1 text-sm text-gray-900">
                  {{ client.open_date }}{{ getDateSuffix(client.open_date) }} - 
                  {{ client.close_date }}{{ getDateSuffix(client.close_date) }} of month
                </dd>
              </div>
            </div>
          </div>

          <!-- Payment Configuration -->
          <div class="space-y-4">
            <h4 class="text-sm font-medium text-gray-900 uppercase tracking-wide">Payment Configuration</h4>
            <div class="space-y-3">
              <div>
                <dt class="text-sm font-medium text-gray-500">B2C PayBill</dt>
                <dd class="mt-1 text-sm text-gray-900 font-mono">{{ client.b2c_paybill || 'Not configured' }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">C2B PayBill</dt>
                <dd class="mt-1 text-sm text-gray-900 font-mono">{{ client.c2b_paybill || 'Not configured' }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">B2B PayBill</dt>
                <dd class="mt-1 text-sm text-gray-900 font-mono">{{ client.b2b_paybill || 'Not configured' }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">SMS Sender ID</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ client.sender_id || 'Not configured' }}</dd>
              </div>
            </div>
          </div>

          <!-- Dates -->
          <div class="space-y-4">
            <h4 class="text-sm font-medium text-gray-900 uppercase tracking-wide">Important Dates</h4>
            <div class="space-y-3">
              <div>
                <dt class="text-sm font-medium text-gray-500">Created</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ formatDateTime(client.created) }}</dd>
              </div>
              <div v-if="client.activated_on">
                <dt class="text-sm font-medium text-gray-500">Activated</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ formatDateTime(client.activated_on) }}</dd>
              </div>
              <div v-if="client.deactivated_on">
                <dt class="text-sm font-medium text-gray-500">Deactivated</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ formatDateTime(client.deactivated_on) }}</dd>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Tabs Section -->
    <div v-if="client" class="bg-white rounded-xl shadow-sm border border-gray-200">
      <!-- Tab Navigation -->
      <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            @click="switchTab(tab.id)"
            :class="[
              activeTab === tab.id
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
              'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center'
            ]"
          >
            <component :is="tab.icon" class="h-5 w-5 mr-2" />
            {{ tab.name }}
            <span v-if="tab.count !== undefined" class="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs font-medium">
              {{ tab.count }}
            </span>
          </button>
        </nav>
      </div>

      <!-- Tab Content -->
      <div class="p-6">
        <!-- Loan Accounts Tab -->
        <div v-if="activeTab === 'loan-accounts'" class="space-y-4">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Loan Accounts</h3>
            <ActionButton variant="add" size="sm" shape="rounded">
              <template #icon>
                <PlusIcon class="h-4 w-4" />
              </template>
              Add Account
            </ActionButton>
          </div>

          <div v-if="tabLoading" class="flex justify-center py-8">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          </div>

          <div v-else-if="loanAccounts.length === 0" class="text-center py-12 text-gray-500">
            <BanknotesIcon class="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>No loan accounts found for this client.</p>
          </div>

          <div v-else class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Account No</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">DOB</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Wallet Balance</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loan Balance</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">KYC Status</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Account Status</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="account in loanAccounts" :key="account.loan_number">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ account.loan_number }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ account.first_name }} {{ account.last_name }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ account.dob }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatCurrency(account.actual_balance) }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatCurrency(account.loan_balance) }}</td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span :class="account.kyc_confirm === '1' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                      {{ account.kyc_confirm === '1' ? 'Completed' : 'Not Completed' }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span :class="account.status === '1000' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                      {{ account.status === '1000' ? 'Active' : 'Inactive' }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatDateTime(account.created) }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Loan Requests Tab -->
        <div v-if="activeTab === 'loan-requests'" class="space-y-4">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Loan Requests</h3>
          </div>

          <div v-if="tabLoading" class="flex justify-center py-8">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          </div>

          <div v-else-if="loanRequests.length === 0" class="text-center py-12 text-gray-500">
            <DocumentTextIcon class="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>No loan requests found for this client.</p>
          </div>

          <div v-else class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Request No</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Requested</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Approved</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="request in loanRequests" :key="request.req_number">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ request.req_number }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ request.product_name || 'N/A' }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatCurrency(request.requested_amount) }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatCurrency(request.approved_amount) }}</td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span :class="getStatusClass(request.approval_status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                      {{ getStatusText(request.approval_status) }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatDateTime(request.created) }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Limits Tab -->
        <div v-if="activeTab === 'limits'" class="space-y-4">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Client Limits</h3>
            <ActionButton variant="edit" size="sm" shape="rounded">
              <template #icon>
                <PencilIcon class="h-4 w-4" />
              </template>
              Edit Limits
            </ActionButton>
          </div>

          <div v-if="tabLoading" class="flex justify-center py-8">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          </div>

          <div v-else-if="limits.length === 0" class="text-center py-12 text-gray-500">
            <ScaleIcon class="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>No limits configured for this client.</p>
          </div>

          <div v-else class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reference</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current Limit</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Requested Limit</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="limit in limits" :key="limit.reference_id">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ limit.reference_id }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatCurrency(limit.current_limit) }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatCurrency(limit.requested_limit) }}</td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span :class="getLimitStatusClass(limit.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                      {{ getLimitStatusText(limit.status) }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatDateTime(limit.created) }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Repayments Tab -->
        <div v-if="activeTab === 'repayments'" class="space-y-4">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Repayments</h3>
          </div>

          <div v-if="tabLoading" class="flex justify-center py-8">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          </div>

          <div v-else-if="repayments.length === 0" class="text-center py-12 text-gray-500">
            <CreditCardIcon class="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>No repayments found for this client.</p>
          </div>

          <div v-else class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Transaction Code</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount Paid</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Balance</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Source</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="repayment in repayments" :key="repayment.trxn_code">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ repayment.trxn_code }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ repayment.payer }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatCurrency(repayment.repayment_amount) }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatCurrency(repayment.reducing_balance) }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ repayment.repayment_source }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatDateTime(repayment.created) }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- KYCs Tab -->
        <div v-if="activeTab === 'kycs'" class="space-y-4">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">KYC Documents</h3>
            <ActionButton variant="add" size="sm" shape="rounded">
              <template #icon>
                <PlusIcon class="h-4 w-4" />
              </template>
              Upload Document
            </ActionButton>
          </div>
          <div class="text-center py-12 text-gray-500">
            <DocumentCheckIcon class="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>No KYC documents found for this client.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  ArrowLeftIcon,
  PencilIcon,
  CheckIcon,
  XMarkIcon,
  BanknotesIcon,
  DocumentTextIcon,
  ScaleIcon,
  CreditCardIcon,
  DocumentCheckIcon,
  PlusIcon
} from '@heroicons/vue/24/outline'
import ActionButton from '@/components/ActionButton.vue'
import { clientsApi } from '@/services/clientsApi'
import type { ClientEntity } from '@/services/types'

const route = useRoute()
const router = useRouter()

// Reactive data
const loading = ref(true)
const tabLoading = ref(false)
const error = ref('')
const client = ref<ClientEntity | null>(null)
const activeTab = ref('loan-accounts')

// Tab data
const loanAccounts = ref([])
const loanRequests = ref([])
const repayments = ref([])
const limits = ref([])

// Tab configuration
const tabs = [
  { id: 'loan-accounts', name: 'Loan Accounts', icon: BanknotesIcon, count: 0 },
  { id: 'loan-requests', name: 'Requests', icon: DocumentTextIcon, count: 0 },
  { id: 'limits', name: 'Limits', icon: ScaleIcon },
  { id: 'repayments', name: 'Repayments', icon: CreditCardIcon, count: 0 },
  { id: 'kycs', name: 'KYCs', icon: DocumentCheckIcon, count: 0 }
]

// Methods
const fetchClientDetails = async () => {
  const clientId = route.params.id as string
  if (!clientId) {
    error.value = 'Client ID is required'
    loading.value = false
    return
  }

  try {
    loading.value = true

    // First try to get client from stored clients list
    const storedClients = JSON.parse(localStorage.getItem('clients') || '[]')
    const storedClient = storedClients.find((c: any) => c.client_id === clientId)

    if (storedClient) {
      client.value = storedClient
      // Fetch additional data for tabs
      await fetchTabData()
    } else {
      // Fallback to API call
      const response = await clientsApi.getClientForEdit(clientId)

      if (response.status === 200) {
        client.value = response.message as ClientEntity
        await fetchTabData()
      } else {
        error.value = response.message as string || 'Failed to load client details'
      }
    }
  } catch (err: any) {
    error.value = err.message || 'An error occurred while loading client details'
  } finally {
    loading.value = false
  }
}

const fetchTabData = async () => {
  if (!client.value) return

  try {
    // Fetch data for all tabs to get counts
    const [loanAccountsRes, loanRequestsRes, repaymentsRes, limitsRes] = await Promise.all([
      clientsApi.getClientLoanAccounts({ client_id: client.value.client_id, limit: 1 }),
      clientsApi.getClientLoanRequests({ client_id: client.value.client_id, limit: 1 }),
      clientsApi.getClientLoanRepayments({ client_id: client.value.client_id, limit: 1 }),
      clientsApi.getClientLimitRequests({ client_id: client.value.client_id, limit: 1 })
    ])

    // Update tab counts
    tabs.forEach(tab => {
      switch (tab.id) {
        case 'loan-accounts':
          tab.count = loanAccountsRes.status === 200 ? (loanAccountsRes.message?.total_count || 0) : 0
          break
        case 'loan-requests':
          tab.count = loanRequestsRes.status === 200 ? (loanRequestsRes.message?.total_count || 0) : 0
          break
        case 'repayments':
          tab.count = repaymentsRes.status === 200 ? (repaymentsRes.message?.total_count || 0) : 0
          break
        case 'limits':
          tab.count = limitsRes.status === 200 ? (limitsRes.message?.total_count || 0) : 0
          break
        case 'kycs':
          tab.count = 0 // KYC count would come from a different endpoint
          break
      }
    })
  } catch (error) {
    console.error('Error fetching tab data:', error)
  }
}

const editClient = () => {
  if (client.value) {
    router.push({ name: 'clients-edit', params: { id: client.value.client_id } })
  }
}

const toggleStatus = async () => {
  if (!client.value) return

  // Implementation for status toggle
  console.log('Toggle status for client:', client.value.client_name)
}

// Tab data fetching methods
const fetchLoanAccounts = async () => {
  if (!client.value) return

  tabLoading.value = true
  try {
    const response = await clientsApi.getClientLoanAccounts({
      client_id: client.value.client_id,
      limit: 10,
      offset: 1
    })

    if (response.status === 200) {
      loanAccounts.value = response.message?.data || []
    }
  } catch (err) {
    console.error('Error fetching loan accounts:', err)
  } finally {
    tabLoading.value = false
  }
}

const fetchLoanRequests = async () => {
  if (!client.value) return

  tabLoading.value = true
  try {
    const response = await clientsApi.getClientLoanRequests({
      client_id: client.value.client_id,
      limit: 10,
      offset: 1
    })

    if (response.status === 200) {
      loanRequests.value = response.message?.data || []
    }
  } catch (err) {
    console.error('Error fetching loan requests:', err)
  } finally {
    tabLoading.value = false
  }
}

const fetchRepayments = async () => {
  if (!client.value) return

  tabLoading.value = true
  try {
    const response = await clientsApi.getClientLoanRepayments({
      client_id: client.value.client_id,
      limit: 10,
      offset: 1
    })

    if (response.status === 200) {
      repayments.value = response.message?.data || []
    }
  } catch (err) {
    console.error('Error fetching repayments:', err)
  } finally {
    tabLoading.value = false
  }
}

const fetchLimits = async () => {
  if (!client.value) return

  tabLoading.value = true
  try {
    const response = await clientsApi.getClientLimitRequests({
      client_id: client.value.client_id,
      limit: 10,
      offset: 1
    })

    if (response.status === 200) {
      limits.value = response.message?.data || []
    }
  } catch (err) {
    console.error('Error fetching limits:', err)
  } finally {
    tabLoading.value = false
  }
}

// Tab switching
const switchTab = async (tabId: string) => {
  activeTab.value = tabId

  switch (tabId) {
    case 'loan-accounts':
      await fetchLoanAccounts()
      break
    case 'loan-requests':
      await fetchLoanRequests()
      break
    case 'repayments':
      await fetchRepayments()
      break
    case 'limits':
      await fetchLimits()
      break
  }
}

// Status helper functions
const getStatusClass = (status: string) => {
  const statusNum = parseInt(status)
  switch (statusNum) {
    case 1: return 'bg-green-100 text-green-800'
    case 2: return 'bg-orange-100 text-orange-800'
    case 3: return 'bg-red-100 text-red-800'
    case 4: return 'bg-purple-100 text-purple-800'
    case 5: return 'bg-blue-100 text-blue-800'
    case 6: return 'bg-yellow-100 text-yellow-800'
    case 7: return 'bg-red-100 text-red-800'
    case 8: return 'bg-green-100 text-green-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getStatusText = (status: string) => {
  const statusNum = parseInt(status)
  switch (statusNum) {
    case 1: return 'Fully Paid'
    case 2: return 'Partially Paid'
    case 3: return 'Rejected'
    case 4: return 'Unverified'
    case 5: return 'Pending'
    case 6: return 'Unpaid'
    case 7: return 'Failure'
    case 8: return 'Approved'
    default: return 'Unknown'
  }
}

const getLimitStatusClass = (status: string) => {
  const statusNum = parseInt(status)
  switch (statusNum) {
    case 1: return 'bg-green-100 text-green-800'
    case 2: return 'bg-orange-100 text-orange-800'
    case 3: return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getLimitStatusText = (status: string) => {
  const statusNum = parseInt(status)
  switch (statusNum) {
    case 1: return 'Approved'
    case 2: return 'Pending'
    case 3: return 'Rejected'
    default: return 'Unknown'
  }
}

// Utility functions
const formatCurrency = (amount: string | number) => {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
  return new Intl.NumberFormat('en-KE', {
    style: 'currency',
    currency: 'KES'
  }).format(numAmount || 0)
}

const formatDateTime = (dateString: string) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('en-KE', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getDateSuffix = (day: string): string => {
  const dayNum = parseInt(day)
  if (dayNum >= 11 && dayNum <= 13) {
    return 'th'
  }
  switch (dayNum % 10) {
    case 1: return 'st'
    case 2: return 'nd'
    case 3: return 'rd'
    default: return 'th'
  }
}

onMounted(async () => {
  await fetchClientDetails()
  // Load the first tab data
  if (client.value) {
    await switchTab('loan-accounts')
  }
})
</script>
